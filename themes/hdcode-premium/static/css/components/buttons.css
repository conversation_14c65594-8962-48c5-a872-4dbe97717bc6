/**
 * Button Components
 * HD Digital Premium Theme
 * 
 * Comprehensive button styles including primary, secondary,
 * and utility buttons with various states and effects.
 */

/* ===== BASE BUTTON STYLES ===== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    border: none;
    border-radius: var(--radius-2xl);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* ===== PRIMARY BUTTON ===== */

.primary-button,
.btn-primary {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-2xl);
    background: var(--gradient-primary);
    color: var(--text-primary);
    text-decoration: none;
    border: none;
    border-radius: var(--radius-2xl);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.primary-button::before,
.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.8) 20%,
        rgba(255, 255, 255, 0.9) 50%,
        rgba(255, 255, 255, 0.8) 80%,
        transparent
    );
    transition: left 0.6s ease;
}

.primary-button:hover::before,
.btn-primary:hover::before {
    left: 100%;
}

.primary-button:hover,
.btn-primary:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.4);
}

/* ===== SECONDARY BUTTON ===== */

.secondary-button,
.btn-secondary {
    padding: var(--spacing-lg) var(--spacing-2xl);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border: 2px solid rgba(255, 255, 255, 0.15);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    backdrop-filter: blur(20px);
}

.secondary-button::before,
.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.secondary-button:hover::before,
.btn-secondary:hover::before {
    opacity: 1;
}

.secondary-button:hover,
.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(59, 130, 246, 0.5);
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* ===== OUTLINE BUTTON ===== */

.btn-outline {
    background: transparent;
    color: var(--accent-primary);
    border: 2px solid var(--accent-primary);
}

.btn-outline:hover {
    background: var(--accent-primary);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* ===== GHOST BUTTON ===== */

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid transparent;
}

.btn-ghost:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.1);
}

/* ===== BUTTON SIZES ===== */

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-lg);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--font-size-lg);
    border-radius: var(--radius-3xl);
}

.btn-xl {
    padding: var(--spacing-xl) var(--spacing-3xl);
    font-size: var(--font-size-xl);
    border-radius: var(--radius-3xl);
}

/* ===== BUTTON VARIANTS ===== */

.btn-success {
    background: var(--accent-secondary);
    color: var(--text-primary);
}

.btn-success:hover {
    background: #0d9488;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
}

.btn-warning {
    background: var(--accent-tertiary);
    color: var(--text-primary);
}

.btn-warning:hover {
    background: #d97706;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
}

.btn-danger {
    background: #ef4444;
    color: var(--text-primary);
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
}

/* ===== SPECIAL BUTTONS ===== */

.email-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    background: var(--gradient-primary);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-2xl);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.email-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* ===== LOADING STATE ===== */

.btn.loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.btn.loading .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
    margin-right: var(--spacing-sm);
}

/* ===== BUTTON GROUPS ===== */

.btn-group {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-group .btn {
    flex: 1;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .primary-button,
    .secondary-button,
    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }

    .btn-group {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .email-button {
        width: 100%;
        justify-content: center;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }
}

@media (max-width: 480px) {
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .btn-sm {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
}

/* ===== ACCESSIBILITY ===== */

.btn:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
    .btn {
        transition: none;
    }
    
    .btn:hover {
        transform: none;
    }
}
